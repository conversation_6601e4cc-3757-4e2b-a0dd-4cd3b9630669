@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Safari-style scrollbars */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
}

/* Webkit browsers (Chrome, Safari, Edge) */
*::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

*::-webkit-scrollbar-track {
  background: transparent;
}

*::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

*::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

*::-webkit-scrollbar-corner {
  background: transparent;
}

/* Hide scrollbars on mobile/touch devices */
@media (hover: none) and (pointer: coarse) {
  * {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
  
  *::-webkit-scrollbar {
    display: none;
  }
}

/* Specific styling for dark backgrounds */
.dark *::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.15);
}

.dark *::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.25);
}

/* Light theme scrollbars */
.light *::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.15);
}

.light *::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.25);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar for specific components that need more visibility */
.scrollbar-visible::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.scrollbar-visible::-webkit-scrollbar-thumb {
  background: rgba(168, 85, 247, 0.3);
  border-radius: 4px;
}

.scrollbar-visible::-webkit-scrollbar-thumb:hover {
  background: rgba(168, 85, 247, 0.5);
}