# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Cloudflare R2 Configuration
VITE_CLOUDFLARE_R2_ACCOUNT_ID=your_account_id_here
VITE_CLOUDFLARE_R2_ACCESS_KEY_ID=your_access_key_id_here
VITE_CLOUDFLARE_R2_SECRET_ACCESS_KEY=your_secret_access_key_here
VITE_CLOUDFLARE_R2_BUCKET_NAME=tasker
VITE_CLOUDFLARE_R2_ENDPOINT=https://your_account_id.r2.cloudflarestorage.com

# Google OAuth (configured in Supabase dashboard)
# No additional env vars needed for Google OAuth with Supabase

# Google AI API Key (user-provided in app)
# GOOGLE_AI_API_KEY=user_provided_in_settings

# Twitter API (user-provided in app)
# TWITTER_API_KEY=user_provided_in_settings
# TWITTER_API_SECRET=user_provided_in_settings