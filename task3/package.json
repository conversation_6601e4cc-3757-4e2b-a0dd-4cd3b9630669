{"name": "tweetscheduler-pro", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@0xindie/agent-twitter-client": "^1.0.0", "@aws-sdk/client-s3": "^3.490.0", "@google/genai": "^1.0.0", "@supabase/supabase-js": "^2.39.0", "crypto-js": "^4.2.0", "date-fns": "^3.6.0", "framer-motion": "^11.0.0", "lucide-react": "^0.344.0", "oauth": "^0.10.0", "react": "^18.3.1", "react-big-calendar": "^1.19.4", "react-dom": "^18.3.1", "react-textarea-autosize": "^8.5.3", "sonner": "^1.4.0"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react-big-calendar": "^1.8.9", "@types/crypto-js": "^4.2.2", "@types/oauth": "^0.9.4", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}