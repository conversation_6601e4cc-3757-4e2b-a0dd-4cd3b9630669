@@ .. @@
 -- Create storage bucket for media files (if not exists)
-INSERT INTO storage.buckets (id, name, public)
-VALUES ('tweet-media', 'tweet-media', true)
-ON CONFLICT (id) DO NOTHING;
-
--- Create storage policies
-CREATE POLICY "Users can upload their own media"
-  ON storage.objects
-  FOR INSERT
-  TO authenticated
-  WITH CHECK (bucket_id = 'tweet-media' AND auth.uid()::text = (storage.foldername(name))[1]);
-
-CREATE POLICY "Users can view their own media"
-  ON storage.objects
-  FOR SELECT
-  TO authenticated
-  USING (bucket_id = 'tweet-media' AND auth.uid()::text = (storage.foldername(name))[1]);
-
-CREATE POLICY "Users can update their own media"
-  ON storage.objects
-  FOR UPDATE
-  TO authenticated
-  USING (bucket_id = 'tweet-media' AND auth.uid()::text = (storage.foldername(name))[1]);
-
-CREATE POLICY "Users can delete their own media"
-  ON storage.objects
-  FOR DELETE
-  TO authenticated
-  USING (bucket_id = 'tweet-media' AND auth.uid()::text = (storage.foldername(name))[1]);