---
description: 
globs: 
alwaysApply: true
---
# 6. Advanced Features & Troubleshooting

This document covers advanced Wasp capabilities like Jobs, API Routes, and Middleware, along with performance optimization tips and common troubleshooting steps.

## Advanced Features ( [main.wasp](mdc:main.wasp) )

These features are configured in [main.wasp](mdc:main.wasp).

### Jobs and Workers

- Wasp supports background jobs, useful for tasks like sending emails, processing data, or scheduled operations.
- Jobs require a job executor like PgBoss (which requires PostgreSQL, see [database-operations.mdc](mdc:template/app/.cursor/rules/database-operations.mdc)).
- Example Job definition in [main.wasp](mdc:main.wasp):
  ```wasp
  job emailSender {
    executor: PgBoss, // Requires PostgreSQL
    // Define the function that performs the job
    perform: {
      fn: import { sendEmail } from "@src/server/jobs/emailSender.js"
    },
    // Grant access to necessary entities
    entities: [User, EmailQueue]
  }
  ```
- Jobs can be scheduled or triggered programmatically from Wasp actions or other jobs.
- See the Wasp Recurring Jobs Docs for more info [wasp-overview.mdc](mdc:template/app/.cursor/rules/wasp-overview.mdc)

### Custom HTTP API Endpoints

- Define custom server API endpoints, often used for external integrations (webhooks, third-party services) where Wasp Operations are not suitable.
- Example API route definition in [main.wasp](mdc:main.wasp):
  ```wasp
  api stripeWebhook {
    // Implementation function in server code
    fn: import { handleStripeWebhook } from "@src/server/apis/stripe.js",
    // Define the HTTP method and path
    httpRoute: (POST, "/webhooks/stripe"),
    // Optional: Grant entity access
    entities: [User, Payment],
    // Optional: Apply middleware config function 
    // middlewareConfigFn: import { apiMiddleware } from "@src/apis"
    // Optional: If auth is enabled, this will default to true and provide a context.user
    // object. If you do not wish to attempt to parse the JWT in the Authorization Header
    // you should set this to false.
    // auth: false
  }
  ```
- See the Wasp Custom HTTP API Endpoints docs for more info [wasp-overview.mdc](mdc:template/app/.cursor/rules/wasp-overview.mdc)

### Middleware

- Wasp supports custom middleware functions that can run before API route handlers or Page components.
- Useful for logging, custom checks, request transformation, etc.
- Example Middleware definition in [main.wasp](mdc:main.wasp):
  ```wasp
  // Customize global middleware
  app todoApp {
  // ...
    server: {
      middlewareConfigFn: import { serverMiddlewareFn } from "@src/serverSetup"
    },
  }
  ```
- See the Wasp Middleware Docs for more info [wasp-overview.mdc](mdc:template/app/.cursor/rules/wasp-overview.mdc)

## Performance Optimization

- **Operation Dependencies:** Use specific entity dependencies (`entities: [Task]`) in your Wasp operations ([main.wasp](mdc:main.wasp)) to ensure queries are automatically refetched only when relevant data changes.
- **Pagination:** For queries returning large lists of data, implement pagination logic in your server operation and corresponding UI controls on the client.
- **React Optimization:**
  - Use `React.memo` for components that re-render often with the same props.
  - Use `useMemo` to memoize expensive calculations within components.
  - Use `useCallback` to memoize functions passed down as props to child components (especially event handlers).
- **Optimistic UI Updates (Actions):**
  - For actions where perceived speed is critical (e.g., deleting an item, marking as complete), consider using Wasp's `useAction` hook (from `wasp/client/operations`) with `optimisticUpdates`.
  - This updates the client-side cache (affecting relevant `useQuery` results) *before* the action completes on the server, providing instant feedback.
  - **Use Sparingly:** Only implement optimistic updates where the action is highly likely to succeed and the instant feedback significantly improves UX. Remember to handle potential server-side failures gracefully (Wasp helps revert optimistic updates on error).
  - See the Wasp Actions docs for more info [wasp-overview.mdc](mdc:template/app/.cursor/rules/wasp-overview.mdc)

## Troubleshooting

- **Wasp Type/Import Errors:** If you encounter TypeScript errors related to missing Wasp imports (e.g., from `wasp/client/operations`, `wasp/entities`, `wasp/server`) or unexpected type mismatches after modifying [main.wasp](mdc:main.wasp) or [schema.prisma](mdc:schema.prisma) , **prompt the user to restart the Wasp development server** (`wasp start`) before further debugging. Wasp needs to regenerate code based on these changes.
- **Operations Not Working:**
  - Check that all required `entities` are listed in the operation's definition in [main.wasp](mdc:main.wasp).
  - Verify the import path (`fn: import { ... } from "@src/..."`) in [main.wasp](mdc:main.wasp) is correct.
  - Check for runtime errors in the Wasp server console where `wasp start` is running.
  - Ensure client-side calls match the expected arguments and types.
- **Auth Not Working:**
  - Verify the `auth` configuration in [main.wasp](mdc:main.wasp) (correct `userEntity`, `methods`, `onAuthFailedRedirectTo`).
  - Ensure `userEntity` in [main.wasp](mdc:main.wasp) matches the actual `User` model name in [schema.prisma](mdc:schema.prisma).
  - Check Wasp server logs for auth-related errors.
  - If using social auth, confirm environment variables (e.g., `GOOGLE_CLIENT_ID`) are correctly set (e.g., in a `.env.server` file) and loaded by Wasp.
- **Database Issues:**
  - Ensure your [schema.prisma](mdc:schema.prisma) syntax is correct.
  - Run `wasp db migrate-dev "Migration description"` after schema changes to apply them.
  - If using PostgreSQL, ensure the database server is running.
  - Check the `.env.server` file for the correct `DATABASE_URL`.
- **Build/Runtime Errors:**
  - Check import paths carefully (Wasp vs. relative vs. `@src/` rules, see [project-conventions.mdc](mdc:template/app/.cursor/rules/project-conventions.mdc)).
  - Ensure all dependencies are installed (`npm install`).
  - Check the Wasp server console and the browser's developer console for specific error messages.

  ### Referencing Wasp Documentation
  - Search for and reference applicable LLM-optimized docs, available in [wasp-overview.mdc](mdc:template/app/.cursor/rules/wasp-overview.mdc)   
