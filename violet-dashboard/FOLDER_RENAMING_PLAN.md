# TweetCraft AI - REVISED Folder Renaming Plan

## 🎯 Overview
**IMPORTANT**: Work WITH the existing OpenSaaS infrastructure, not against it. The template already has robust systems for auth, payments, file uploads, analytics, and AI integration.

## ⚠️ **What NOT to Touch (Already Perfect)**

### **✅ Keep These Folders As-Is**
- `auth/` - Complete authentication system (email, Google OAuth ready)
- `payment/` - Full Stripe/LemonSqueezy integration with subscriptions
- `file-upload/` - S3-compatible system (works with Cloudflare R2!)
- `analytics/` - Analytics dashboard with cron jobs
- `admin/` - Complete admin dashboard
- `user/` - User management system
- `client/`, `server/`, `shared/`, `landing-page/` - Core infrastructure

## 📁 Smart Transformation Strategy

### **Phase 1: Extend Existing Systems (Don't Replace)**

```
violet-dashboard/app/src/
├── auth/                     → auth/                     (KEEP - Add Twitter OAuth)
├── payment/                  → payment/                  (KEEP - Add Twitter plans)
├── file-upload/              → file-upload/              (KEEP - Perfect for media)
├── analytics/                → analytics/                (KEEP - Add tweet metrics)
├── admin/                    → admin/                    (KEEP - Add Twitter admin)
├── demo-ai-app/              → tweet-composer/           (TRANSFORM - Extend AI system)
├── messages/                 → notifications/            (RENAME - User notifications)
└── NEW TWITTER-SPECIFIC FOLDERS:
    ├── twitter-integration/                              (NEW - Twitter API wrapper)
    ├── tweet-scheduler/                                  (NEW - Extends existing jobs)
    ├── ai-agents/                                        (NEW - Extends AI system)
    └── content-calendar/                                 (NEW - UI for scheduling)
```

## 🔄 Smart Extension Strategy

### 1. demo-ai-app/ → tweet-composer/ (EXTEND, Don't Break)
**Purpose**: Transform the existing AI system into Twitter content generation

**What's Already Perfect**:
- ✅ OpenAI integration with function calling
- ✅ Credit-based usage system
- ✅ User authentication checks
- ✅ Structured AI responses
- ✅ Database operations with Prisma

**Smart Changes**:
```typescript
// EXTEND the existing operations.ts, don't rewrite
// Keep: generateGptResponse structure
// Change: The AI prompt and response format
// Keep: Credit system and user validation
// Add: Twitter-specific response types

// EXTEND the existing schedule.ts types
// Keep: The structured approach
// Change: MainTask → TweetIdea, SubTask → TweetVariation
```

**Wasp Configuration (Minimal Changes)**:
```wasp
// Keep the existing structure, just update paths
route DemoAppRoute { path: "/compose", to: TweetComposerPage }
page TweetComposerPage {
  authRequired: true,
  component: import TweetComposerPage from "@src/tweet-composer/TweetComposerPage"
}

// Keep existing operations, extend functionality
action generateGptResponse {
  fn: import { generateTweetContent } from "@src/tweet-composer/operations",
  entities: [User, Tweet, GptResponse] // Add Tweet entity
}
```

### 2. file-upload/ → KEEP AS-IS! (Perfect for Twitter Media)
**Why Keep It**: The existing system is PERFECT for Twitter media management

**What's Already Built**:
- ✅ S3-compatible (works with Cloudflare R2 out of the box!)
- ✅ Presigned URLs for secure uploads
- ✅ File validation and size limits
- ✅ User-specific file organization
- ✅ Download URL generation

**Smart Extension Strategy**:
```typescript
// DON'T rename the folder - just extend it
// ADD new files to existing file-upload/:
file-upload/
├── FileUploadPage.tsx           (KEEP - works perfectly)
├── operations.ts                (KEEP - extend with Twitter optimization)
├── s3Utils.ts                   (KEEP - already R2 compatible!)
├── validation.ts                (KEEP - extend with Twitter limits)
└── NEW FILES:
    ├── twitterMediaOptimization.ts  (NEW - Twitter-specific optimization)
    ├── imageCompression.ts           (NEW - 40-70% compression)
    └── videoProcessing.ts            (NEW - Twitter video specs)
```

**Environment Variables (Already Set Up)**:
```bash
# These work with Cloudflare R2 (S3-compatible API)
AWS_S3_IAM_ACCESS_KEY=your-r2-access-key
AWS_S3_IAM_SECRET_KEY=your-r2-secret-key
AWS_S3_FILES_BUCKET=your-r2-bucket
AWS_S3_REGION=auto  # For Cloudflare R2
```

### 3. messages/ → notifications/ (Simple Rename)
**Purpose**: User notifications for tweet scheduling alerts

**Simple Rename**:
```
messages/MessageButton.tsx    → notifications/NotificationButton.tsx
messages/MessagesPage.tsx     → notifications/NotificationsPage.tsx
```

### 4. payment/ → KEEP AS-IS! (Perfect Subscription System)
**Why Keep It**: The payment system is enterprise-grade and complete

**What's Already Built**:
- ✅ Stripe AND LemonSqueezy integration
- ✅ Subscription plans (Hobby, Pro, Credits)
- ✅ Webhook handling for both processors
- ✅ Customer portal integration
- ✅ Credit-based API usage system

**Smart Extension Strategy**:
```typescript
// DON'T rename - just extend the plans
// ADD Twitter-specific plans to existing plans.ts:

export enum PaymentPlanId {
  Hobby = 'hobby',           // KEEP - 50 tweets/month
  Pro = 'pro',               // KEEP - 500 tweets/month
  Credits10 = 'credits10',   // KEEP - 10 AI generations
  // ADD NEW:
  TwitterBasic = 'twitter_basic',     // 100 tweets/month
  TwitterPro = 'twitter_pro',         // 1000 tweets/month
  TwitterBusiness = 'twitter_business' // Unlimited
}
```

**Extend Existing Credit System**:
```typescript
// The credit system already works perfectly for AI usage
// Just extend it for Twitter API calls
```

## 🆕 New Folders to Create

### 1. tweet-scheduler/
**Purpose**: Tweet scheduling functionality

**Files to Create**:
```
tweet-scheduler/
├── SchedulerPage.tsx              (Main scheduling interface)
├── components/
│   ├── ScheduleForm.tsx           (Schedule tweet form)
│   ├── ScheduledTweetsList.tsx    (List of scheduled tweets)
│   └── TimezonePicker.tsx         (Timezone selection)
├── operations.ts                  (Schedule operations)
├── schedulingEngine.ts            (Background scheduling logic)
└── timeUtils.ts                   (Time zone utilities)
```

**Wasp Configuration**:
```wasp
route SchedulerRoute { path: "/schedule", to: SchedulerPage }
page SchedulerPage {
  authRequired: true,
  component: import SchedulerPage from "@src/tweet-scheduler/SchedulerPage"
}

action scheduleTweet {
  fn: import { scheduleTweet } from "@src/tweet-scheduler/operations",
  entities: [User, Tweet, ScheduledTweet]
}

action cancelScheduledTweet {
  fn: import { cancelScheduledTweet } from "@src/tweet-scheduler/operations",
  entities: [ScheduledTweet]
}

query getScheduledTweets {
  fn: import { getScheduledTweets } from "@src/tweet-scheduler/operations",
  entities: [User, ScheduledTweet]
}
```

### 2. ai-agents/
**Purpose**: AI persona management

**Files to Create**:
```
ai-agents/
├── AIAgentsPage.tsx               (Manage AI personas)
├── components/
│   ├── AgentCard.tsx              (Individual agent display)
│   ├── CreateAgentForm.tsx        (Create new agent)
│   └── AgentTrainingPanel.tsx     (Train agent with examples)
├── operations.ts                  (Agent CRUD operations)
├── agentPersonalities.ts          (Predefined personalities)
└── agentTraining.ts               (Training logic)
```

**Wasp Configuration**:
```wasp
route AIAgentsRoute { path: "/agents", to: AIAgentsPage }
page AIAgentsPage {
  authRequired: true,
  component: import AIAgentsPage from "@src/ai-agents/AIAgentsPage"
}

action createAIAgent {
  fn: import { createAIAgent } from "@src/ai-agents/operations",
  entities: [User, AIAgent]
}

action updateAIAgent {
  fn: import { updateAIAgent } from "@src/ai-agents/operations",
  entities: [AIAgent]
}

query getUserAIAgents {
  fn: import { getUserAIAgents } from "@src/ai-agents/operations",
  entities: [User, AIAgent]
}
```

### 3. twitter-integration/
**Purpose**: Twitter API integration

**Files to Create**:
```
twitter-integration/
├── TwitterConnectionPage.tsx      (Connect Twitter account)
├── components/
│   ├── TwitterAccountCard.tsx     (Connected account display)
│   ├── OAuthCallback.tsx          (OAuth callback handler)
│   └── TwitterPermissions.tsx     (Permission management)
├── operations.ts                  (Twitter operations)
├── twitterAPI.ts                  (Twitter API client)
├── oauthHandler.ts                (OAuth 2.0 implementation)
└── tokenManager.ts                (Token refresh logic)
```

**Wasp Configuration**:
```wasp
route TwitterConnectionRoute { path: "/twitter/connect", to: TwitterConnectionPage }
page TwitterConnectionPage {
  authRequired: true,
  component: import TwitterConnectionPage from "@src/twitter-integration/TwitterConnectionPage"
}

action connectTwitterAccount {
  fn: import { connectTwitterAccount } from "@src/twitter-integration/operations",
  entities: [User, TwitterAccount]
}

action postTweetToTwitter {
  fn: import { postTweetToTwitter } from "@src/twitter-integration/operations",
  entities: [User, Tweet, TwitterAccount]
}

query getConnectedTwitterAccounts {
  fn: import { getConnectedTwitterAccounts } from "@src/twitter-integration/operations",
  entities: [User, TwitterAccount]
}
```

### 4. content-calendar/
**Purpose**: Calendar view for content planning

**Files to Create**:
```
content-calendar/
├── CalendarPage.tsx               (Main calendar interface)
├── components/
│   ├── CalendarGrid.tsx           (Calendar grid component)
│   ├── TweetPreview.tsx           (Tweet preview in calendar)
│   ├── DatePicker.tsx             (Date selection)
│   └── CalendarFilters.tsx        (Filter options)
├── operations.ts                  (Calendar operations)
├── calendarUtils.ts               (Date/time utilities)
└── calendarTypes.ts               (Calendar-specific types)
```

**Wasp Configuration**:
```wasp
route CalendarRoute { path: "/calendar", to: CalendarPage }
page CalendarPage {
  authRequired: true,
  component: import CalendarPage from "@src/content-calendar/CalendarPage"
}

query getCalendarTweets {
  fn: import { getCalendarTweets } from "@src/content-calendar/operations",
  entities: [User, Tweet, ScheduledTweet]
}

action rescheduleFromCalendar {
  fn: import { rescheduleFromCalendar } from "@src/content-calendar/operations",
  entities: [ScheduledTweet]
}
```

### 5. dashboard/
**Purpose**: Main application dashboard

**Files to Create**:
```
dashboard/
├── DashboardPage.tsx              (Main dashboard)
├── components/
│   ├── DashboardStats.tsx         (Key metrics)
│   ├── RecentTweets.tsx           (Recent activity)
│   ├── UpcomingScheduled.tsx      (Upcoming tweets)
│   └── QuickActions.tsx           (Quick action buttons)
├── operations.ts                  (Dashboard operations)
└── dashboardTypes.ts              (Dashboard-specific types)
```

**Wasp Configuration**:
```wasp
route DashboardRoute { path: "/dashboard", to: DashboardPage }
page DashboardPage {
  authRequired: true,
  component: import DashboardPage from "@src/dashboard/DashboardPage"
}

query getDashboardStats {
  fn: import { getDashboardStats } from "@src/dashboard/operations",
  entities: [User, Tweet, ScheduledTweet, TwitterAccount]
}
```

## 🔧 Implementation Steps

### Phase 1: Rename Existing Folders (Week 1)
1. **demo-ai-app** → **tweet-composer**
2. **file-upload** → **media-manager**
3. **messages** → **notifications**
4. **payment** → **billing**

### Phase 2: Create New Folders (Week 2)
1. Create **tweet-scheduler** folder and basic structure
2. Create **ai-agents** folder and basic structure
3. Create **twitter-integration** folder and basic structure

### Phase 3: Advanced Features (Week 3-4)
1. Create **content-calendar** folder and components
2. Create **dashboard** folder and main interface
3. Update all Wasp configurations and routes

### Phase 4: Testing & Refinement (Week 5)
1. Test all renamed imports and routes
2. Update any remaining references
3. Verify all functionality works correctly

## ⚠️ Important Notes

1. **Update Import Statements**: All import statements in components and operations must be updated
2. **Wasp Configuration**: Update all route paths and component imports in main.wasp
3. **Database Entities**: May need to rename entities (Task → Tweet, File → MediaFile)
4. **Environment Variables**: Update any environment variable references
5. **Documentation**: Update README and other docs with new structure

## 🧪 Testing Checklist

- [ ] All pages load correctly with new paths
- [ ] All operations work with renamed functions
- [ ] Import statements are updated throughout codebase
- [ ] Wasp compilation succeeds without errors
- [ ] Database operations work with renamed entities
- [ ] File uploads work with new media-manager structure
- [ ] Authentication flows remain intact
- [ ] Admin dashboard functions properly

This renaming plan transforms the generic SaaS template into a contextually relevant Twitter management application while maintaining all Wasp framework conventions and functionality.
