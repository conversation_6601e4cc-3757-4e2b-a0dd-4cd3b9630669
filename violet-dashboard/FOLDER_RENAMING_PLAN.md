# TweetCraft AI - Folder Renaming Plan

## 🎯 Overview
Transform the generic Wasp SaaS template into a Twitter-focused application structure. This plan maintains Wasp conventions while making the codebase contextually relevant to our Twitter scheduling app.

## 📁 Complete Folder Structure Transformation

### Current Structure → New Structure

```
violet-dashboard/app/src/
├── admin/                    → admin/                    (KEEP - Admin dashboard)
├── analytics/                → analytics/                (KEEP - Tweet analytics)
├── auth/                     → auth/                     (KEEP - User authentication)
├── client/                   → client/                   (KEEP - Client-side code)
├── demo-ai-app/              → tweet-composer/           (RENAME - AI tweet generation)
├── file-upload/              → media-manager/            (RENAME - Image/video handling)
├── landing-page/             → landing-page/             (KEEP - Marketing site)
├── messages/                 → notifications/            (RENAME - User notifications)
├── payment/                  → billing/                  (RENAME - Subscription management)
├── server/                   → server/                   (KEEP - Server utilities)
├── shared/                   → shared/                   (KEEP - Shared utilities)
├── user/                     → user/                     (KEEP - User management)
└── NEW FOLDERS TO CREATE:
    ├── tweet-scheduler/                                  (NEW - Scheduling system)
    ├── ai-agents/                                        (NEW - AI persona management)
    ├── twitter-integration/                              (NEW - Twitter API)
    ├── content-calendar/                                 (NEW - Calendar interface)
    └── dashboard/                                        (NEW - Main app dashboard)
```

## 🔄 Detailed Renaming Instructions

### 1. demo-ai-app/ → tweet-composer/
**Purpose**: Transform generic AI demo into Twitter content generation

**Files to Rename**:
```
demo-ai-app/DemoAppPage.tsx        → tweet-composer/TweetComposerPage.tsx
demo-ai-app/operations.ts          → tweet-composer/operations.ts
demo-ai-app/schedule.ts            → tweet-composer/aiGeneration.ts
```

**Wasp Configuration Updates**:
```wasp
// OLD
route DemoAppRoute { path: "/demo-app", to: DemoAppPage }
page DemoAppPage {
  component: import DemoAppPage from "@src/demo-ai-app/DemoAppPage"
}

// NEW
route TweetComposerRoute { path: "/compose", to: TweetComposerPage }
page TweetComposerPage {
  authRequired: true,
  component: import TweetComposerPage from "@src/tweet-composer/TweetComposerPage"
}
```

**Operations to Rename**:
```wasp
// OLD
action generateGptResponse → action generateTweetContent
action createTask         → action createTweet
action deleteTask         → action deleteTweet
action updateTask         → action updateTweet
query getGptResponses     → query getTweetSuggestions
query getAllTasksByUser   → query getAllTweetsByUser
```

### 2. file-upload/ → media-manager/
**Purpose**: Focus on Twitter media optimization

**Files to Rename**:
```
file-upload/FileUploadPage.tsx     → media-manager/MediaManagerPage.tsx
file-upload/fileUploading.ts       → media-manager/mediaUploading.ts
file-upload/operations.ts          → media-manager/operations.ts
file-upload/s3Utils.ts             → media-manager/cloudflareR2Utils.ts
file-upload/validation.ts          → media-manager/mediaValidation.ts
```

**Wasp Configuration Updates**:
```wasp
// OLD
route FileUploadRoute { path: "/file-upload", to: FileUploadPage }
page FileUploadPage {
  component: import FileUpload from "@src/file-upload/FileUploadPage"
}

// NEW
route MediaManagerRoute { path: "/media", to: MediaManagerPage }
page MediaManagerPage {
  authRequired: true,
  component: import MediaManager from "@src/media-manager/MediaManagerPage"
}
```

**Operations to Rename**:
```wasp
// OLD
action createFile              → action uploadMedia
query getAllFilesByUser        → query getAllMediaByUser
query getDownloadFileSignedURL → query getMediaDownloadURL
```

### 3. messages/ → notifications/
**Purpose**: User notifications and alerts

**Files to Rename**:
```
messages/MessageButton.tsx    → notifications/NotificationButton.tsx
messages/MessagesPage.tsx     → notifications/NotificationsPage.tsx
```

**Wasp Configuration Updates**:
```wasp
// OLD
route AdminMessagesRoute { path: "/admin/messages", to: AdminMessagesPage }
page AdminMessagesPage {
  component: import AdminMessages from "@src/messages/MessagesPage"
}

// NEW
route AdminNotificationsRoute { path: "/admin/notifications", to: AdminNotificationsPage }
page AdminNotificationsPage {
  authRequired: true,
  component: import AdminNotifications from "@src/notifications/NotificationsPage"
}
```

### 4. payment/ → billing/
**Purpose**: Subscription and payment management

**Files to Rename**:
```
payment/CheckoutPage.tsx       → billing/CheckoutPage.tsx
payment/PricingPage.tsx        → billing/PricingPage.tsx
payment/operations.ts          → billing/operations.ts
payment/paymentProcessor.ts    → billing/subscriptionProcessor.ts
payment/plans.ts               → billing/subscriptionPlans.ts
payment/webhook.ts             → billing/webhook.ts
payment/errors.ts              → billing/errors.ts
payment/stripe/                → billing/stripe/
payment/lemonSqueezy/          → billing/lemonSqueezy/
```

**Wasp Configuration Updates**:
```wasp
// OLD
route PricingPageRoute { path: "/pricing", to: PricingPage }
route CheckoutRoute { path: "/checkout", to: CheckoutPage }

// NEW
route PricingPageRoute { path: "/pricing", to: PricingPage }
route CheckoutRoute { path: "/checkout", to: CheckoutPage }
// (Keep same routes, just update imports)

page PricingPage {
  component: import PricingPage from "@src/billing/PricingPage"
}
page CheckoutPage {
  authRequired: true,
  component: import Checkout from "@src/billing/CheckoutPage"
}
```

## 🆕 New Folders to Create

### 1. tweet-scheduler/
**Purpose**: Tweet scheduling functionality

**Files to Create**:
```
tweet-scheduler/
├── SchedulerPage.tsx              (Main scheduling interface)
├── components/
│   ├── ScheduleForm.tsx           (Schedule tweet form)
│   ├── ScheduledTweetsList.tsx    (List of scheduled tweets)
│   └── TimezonePicker.tsx         (Timezone selection)
├── operations.ts                  (Schedule operations)
├── schedulingEngine.ts            (Background scheduling logic)
└── timeUtils.ts                   (Time zone utilities)
```

**Wasp Configuration**:
```wasp
route SchedulerRoute { path: "/schedule", to: SchedulerPage }
page SchedulerPage {
  authRequired: true,
  component: import SchedulerPage from "@src/tweet-scheduler/SchedulerPage"
}

action scheduleTweet {
  fn: import { scheduleTweet } from "@src/tweet-scheduler/operations",
  entities: [User, Tweet, ScheduledTweet]
}

action cancelScheduledTweet {
  fn: import { cancelScheduledTweet } from "@src/tweet-scheduler/operations",
  entities: [ScheduledTweet]
}

query getScheduledTweets {
  fn: import { getScheduledTweets } from "@src/tweet-scheduler/operations",
  entities: [User, ScheduledTweet]
}
```

### 2. ai-agents/
**Purpose**: AI persona management

**Files to Create**:
```
ai-agents/
├── AIAgentsPage.tsx               (Manage AI personas)
├── components/
│   ├── AgentCard.tsx              (Individual agent display)
│   ├── CreateAgentForm.tsx        (Create new agent)
│   └── AgentTrainingPanel.tsx     (Train agent with examples)
├── operations.ts                  (Agent CRUD operations)
├── agentPersonalities.ts          (Predefined personalities)
└── agentTraining.ts               (Training logic)
```

**Wasp Configuration**:
```wasp
route AIAgentsRoute { path: "/agents", to: AIAgentsPage }
page AIAgentsPage {
  authRequired: true,
  component: import AIAgentsPage from "@src/ai-agents/AIAgentsPage"
}

action createAIAgent {
  fn: import { createAIAgent } from "@src/ai-agents/operations",
  entities: [User, AIAgent]
}

action updateAIAgent {
  fn: import { updateAIAgent } from "@src/ai-agents/operations",
  entities: [AIAgent]
}

query getUserAIAgents {
  fn: import { getUserAIAgents } from "@src/ai-agents/operations",
  entities: [User, AIAgent]
}
```

### 3. twitter-integration/
**Purpose**: Twitter API integration

**Files to Create**:
```
twitter-integration/
├── TwitterConnectionPage.tsx      (Connect Twitter account)
├── components/
│   ├── TwitterAccountCard.tsx     (Connected account display)
│   ├── OAuthCallback.tsx          (OAuth callback handler)
│   └── TwitterPermissions.tsx     (Permission management)
├── operations.ts                  (Twitter operations)
├── twitterAPI.ts                  (Twitter API client)
├── oauthHandler.ts                (OAuth 2.0 implementation)
└── tokenManager.ts                (Token refresh logic)
```

**Wasp Configuration**:
```wasp
route TwitterConnectionRoute { path: "/twitter/connect", to: TwitterConnectionPage }
page TwitterConnectionPage {
  authRequired: true,
  component: import TwitterConnectionPage from "@src/twitter-integration/TwitterConnectionPage"
}

action connectTwitterAccount {
  fn: import { connectTwitterAccount } from "@src/twitter-integration/operations",
  entities: [User, TwitterAccount]
}

action postTweetToTwitter {
  fn: import { postTweetToTwitter } from "@src/twitter-integration/operations",
  entities: [User, Tweet, TwitterAccount]
}

query getConnectedTwitterAccounts {
  fn: import { getConnectedTwitterAccounts } from "@src/twitter-integration/operations",
  entities: [User, TwitterAccount]
}
```

### 4. content-calendar/
**Purpose**: Calendar view for content planning

**Files to Create**:
```
content-calendar/
├── CalendarPage.tsx               (Main calendar interface)
├── components/
│   ├── CalendarGrid.tsx           (Calendar grid component)
│   ├── TweetPreview.tsx           (Tweet preview in calendar)
│   ├── DatePicker.tsx             (Date selection)
│   └── CalendarFilters.tsx        (Filter options)
├── operations.ts                  (Calendar operations)
├── calendarUtils.ts               (Date/time utilities)
└── calendarTypes.ts               (Calendar-specific types)
```

**Wasp Configuration**:
```wasp
route CalendarRoute { path: "/calendar", to: CalendarPage }
page CalendarPage {
  authRequired: true,
  component: import CalendarPage from "@src/content-calendar/CalendarPage"
}

query getCalendarTweets {
  fn: import { getCalendarTweets } from "@src/content-calendar/operations",
  entities: [User, Tweet, ScheduledTweet]
}

action rescheduleFromCalendar {
  fn: import { rescheduleFromCalendar } from "@src/content-calendar/operations",
  entities: [ScheduledTweet]
}
```

### 5. dashboard/
**Purpose**: Main application dashboard

**Files to Create**:
```
dashboard/
├── DashboardPage.tsx              (Main dashboard)
├── components/
│   ├── DashboardStats.tsx         (Key metrics)
│   ├── RecentTweets.tsx           (Recent activity)
│   ├── UpcomingScheduled.tsx      (Upcoming tweets)
│   └── QuickActions.tsx           (Quick action buttons)
├── operations.ts                  (Dashboard operations)
└── dashboardTypes.ts              (Dashboard-specific types)
```

**Wasp Configuration**:
```wasp
route DashboardRoute { path: "/dashboard", to: DashboardPage }
page DashboardPage {
  authRequired: true,
  component: import DashboardPage from "@src/dashboard/DashboardPage"
}

query getDashboardStats {
  fn: import { getDashboardStats } from "@src/dashboard/operations",
  entities: [User, Tweet, ScheduledTweet, TwitterAccount]
}
```

## 🔧 Implementation Steps

### Phase 1: Rename Existing Folders (Week 1)
1. **demo-ai-app** → **tweet-composer**
2. **file-upload** → **media-manager**
3. **messages** → **notifications**
4. **payment** → **billing**

### Phase 2: Create New Folders (Week 2)
1. Create **tweet-scheduler** folder and basic structure
2. Create **ai-agents** folder and basic structure
3. Create **twitter-integration** folder and basic structure

### Phase 3: Advanced Features (Week 3-4)
1. Create **content-calendar** folder and components
2. Create **dashboard** folder and main interface
3. Update all Wasp configurations and routes

### Phase 4: Testing & Refinement (Week 5)
1. Test all renamed imports and routes
2. Update any remaining references
3. Verify all functionality works correctly

## ⚠️ Important Notes

1. **Update Import Statements**: All import statements in components and operations must be updated
2. **Wasp Configuration**: Update all route paths and component imports in main.wasp
3. **Database Entities**: May need to rename entities (Task → Tweet, File → MediaFile)
4. **Environment Variables**: Update any environment variable references
5. **Documentation**: Update README and other docs with new structure

## 🧪 Testing Checklist

- [ ] All pages load correctly with new paths
- [ ] All operations work with renamed functions
- [ ] Import statements are updated throughout codebase
- [ ] Wasp compilation succeeds without errors
- [ ] Database operations work with renamed entities
- [ ] File uploads work with new media-manager structure
- [ ] Authentication flows remain intact
- [ ] Admin dashboard functions properly

This renaming plan transforms the generic SaaS template into a contextually relevant Twitter management application while maintaining all Wasp framework conventions and functionality.
